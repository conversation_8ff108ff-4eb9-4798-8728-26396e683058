// 1. 语法版本声明（可选）
syntax = "v1"

// 2. 信息块 - 定义 API 的基本信息
info (
	title:   "用户管理系统" // API 标题
	desc:    "提供用户相关的接口" // API 描述
	author:  "张三" // 作者
	email:   "<PERSON><PERSON><PERSON>@example.com" // 邮箱
	version: "1.0.0" // 版本号
)

// 3. 类型定义块 - 定义数据结构
type (
	// 请求结构体
	LoginRequest {
		Username string `json:"username"`
		Password string `json:"password"`
	}
	// 响应结构体
	LoginResponse {
		Token      string `json:"token"`
		ExpireTime int64  `json:"expire_time"`
	}
)

// 4. 服务定义块 - 定义 API 路由和处理器
service user-api {
	@handler login
	post /user/login (LoginRequest) returns (LoginResponse)
}

